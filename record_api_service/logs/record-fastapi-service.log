2025-07-28 03:25:07,465 - uvicorn.error - INFO - server - _serve - Started server process [8]
2025-07-28 03:25:07,465 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 03:25:07,466 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 03:25:07,487 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 03:25:07,487 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,489 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 03:25:07,489 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,490 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 03:25:07,490 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,492 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:25:07,493 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 03:25:07,493 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00092s] {}
2025-07-28 03:25:07,494 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:25:07,494 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 03:25:07,495 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:25:07,495 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 03:25:07,495 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,500 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 03:25:07,500 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,502 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 03:25:07,502 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,504 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 03:25:07,504 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,506 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 03:25:07,506 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:25:07,508 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 03:25:07,508 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 03:25:07,508 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 03:25:07,508 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 03:25:07,509 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-28 03:28:38,175 - uvicorn.error - INFO - server - shutdown - Shutting down
2025-07-28 03:28:38,278 - uvicorn.error - INFO - on - shutdown - Waiting for application shutdown.
2025-07-28 03:28:38,279 - main - INFO - main - lifespan - Shutting down Record FastAPI Service...
2025-07-28 03:28:38,280 - uvicorn.error - INFO - on - shutdown - Application shutdown complete.
2025-07-28 03:28:38,280 - uvicorn.error - INFO - server - _serve - Finished server process [8]
2025-07-28 03:28:41,578 - uvicorn.error - INFO - server - _serve - Started server process [7]
2025-07-28 03:28:41,578 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 03:28:41,579 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 03:28:41,594 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 03:28:41,594 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,595 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 03:28:41,595 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,596 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 03:28:41,596 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,597 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:28:41,597 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 03:28:41,597 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00027s] {}
2025-07-28 03:28:41,597 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:28:41,598 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 03:28:41,598 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:28:41,598 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 03:28:41,598 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,601 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 03:28:41,601 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,602 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 03:28:41,602 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,604 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 03:28:41,604 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,605 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 03:28:41,606 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:28:41,607 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 03:28:41,608 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 03:28:41,608 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 03:28:41,608 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 03:28:41,608 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-28 03:43:02,903 - uvicorn.error - INFO - server - shutdown - Shutting down
2025-07-28 03:43:03,113 - uvicorn.error - INFO - on - shutdown - Waiting for application shutdown.
2025-07-28 03:43:03,156 - main - INFO - main - lifespan - Shutting down Record FastAPI Service...
2025-07-28 03:43:03,194 - uvicorn.error - INFO - on - shutdown - Application shutdown complete.
2025-07-28 03:43:03,207 - uvicorn.error - INFO - server - _serve - Finished server process [7]
2025-07-28 03:43:07,694 - uvicorn.error - INFO - server - _serve - Started server process [8]
2025-07-28 03:43:07,695 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 03:43:07,695 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 03:43:07,732 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 03:43:07,733 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,736 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 03:43:07,736 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,738 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 03:43:07,738 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,740 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:43:07,741 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 03:43:07,741 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00033s] {}
2025-07-28 03:43:07,744 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:43:07,744 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 03:43:07,744 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:43:07,745 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 03:43:07,745 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,870 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 03:43:07,870 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,875 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 03:43:07,875 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,883 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 03:43:07,883 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,887 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 03:43:07,887 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 03:43:07,891 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 03:43:07,893 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 03:43:07,893 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 03:43:07,894 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 03:43:07,894 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-28 03:43:19,890 - uvicorn.access - INFO - httptools_impl - send - **********:43810 - "GET / HTTP/1.1" 200
2025-07-28 03:43:20,097 - uvicorn.access - INFO - httptools_impl - send - **********:43810 - "GET /favicon.ico HTTP/1.1" 404
2025-07-28 03:43:24,697 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:43:24,698 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 03:43:24,698 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 16.96s ago] {}
2025-07-28 03:43:24,698 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:43:24,699 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 03:43:24,699 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:43:24,699 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 03:43:24,699 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00028s] {}
2025-07-28 03:43:24,700 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 03:43:24,700 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00038s] {}
2025-07-28 03:43:24,703 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:43:24,703 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 03:43:24,704 - uvicorn.access - INFO - httptools_impl - send - **********:43828 - "GET /health HTTP/1.1" 200
2025-07-28 03:43:29,518 - uvicorn.access - INFO - httptools_impl - send - **********:58356 - "GET /docs HTTP/1.1" 200
2025-07-28 03:43:30,980 - fastapi - WARNING - models - _validate - email-validator not installed, email fields will be treated as str.
To install, run: pip install email-validator
2025-07-28 03:43:30,989 - uvicorn.access - INFO - httptools_impl - send - **********:58356 - "GET /openapi.json HTTP/1.1" 200
2025-07-28 03:43:33,909 - uvicorn.access - INFO - httptools_impl - send - **********:58372 - "GET /docs HTTP/1.1" 200
2025-07-28 03:43:44,510 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:43:44,510 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 03:43:44,510 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 36.77s ago] {}
2025-07-28 03:43:44,510 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:43:44,511 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 03:43:44,512 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 03:43:44,512 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 03:43:44,512 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 19.81s ago] {}
2025-07-28 03:43:44,512 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 03:43:44,512 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 19.81s ago] {}
2025-07-28 03:43:44,513 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 03:43:44,513 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 03:43:44,514 - uvicorn.access - INFO - httptools_impl - send - **********:50140 - "GET /health HTTP/1.1" 200
2025-07-28 03:43:56,587 - uvicorn.access - INFO - httptools_impl - send - **********:36144 - "GET /api/v1/users/me HTTP/1.1" 403
2025-07-28 08:10:11,875 - uvicorn.error - WARNING - httptools_impl - data_received - Invalid HTTP request received.
2025-07-28 12:55:13,925 - uvicorn.error - INFO - server - shutdown - Shutting down
2025-07-28 12:55:14,102 - uvicorn.error - INFO - on - shutdown - Waiting for application shutdown.
2025-07-28 12:55:14,151 - main - INFO - main - lifespan - Shutting down Record FastAPI Service...
2025-07-28 12:55:14,182 - uvicorn.error - INFO - on - shutdown - Application shutdown complete.
2025-07-28 12:55:14,200 - uvicorn.error - INFO - server - _serve - Finished server process [8]
2025-07-28 13:00:05,902 - uvicorn.error - INFO - server - _serve - Started server process [8]
2025-07-28 13:00:05,903 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 13:00:05,904 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 13:00:05,922 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 13:00:05,922 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,925 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 13:00:05,926 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,926 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 13:00:05,926 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,927 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 13:00:05,928 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 13:00:05,928 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00037s] {}
2025-07-28 13:00:05,928 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 13:00:05,929 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 13:00:05,929 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 13:00:05,929 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 13:00:05,929 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,938 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 13:00:05,939 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,941 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 13:00:05,941 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,943 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 13:00:05,943 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,945 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 13:00:05,945 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:00:05,947 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 13:00:05,948 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 13:00:05,948 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 13:00:05,948 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 13:00:05,948 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-28 13:35:28,260 - uvicorn.error - INFO - server - shutdown - Shutting down
2025-07-28 13:35:28,362 - uvicorn.error - INFO - on - shutdown - Waiting for application shutdown.
2025-07-28 13:35:28,363 - main - INFO - main - lifespan - Shutting down Record FastAPI Service...
2025-07-28 13:35:28,363 - uvicorn.error - INFO - on - shutdown - Application shutdown complete.
2025-07-28 13:35:28,363 - uvicorn.error - INFO - server - _serve - Finished server process [8]
2025-07-28 13:36:33,132 - uvicorn.error - INFO - server - _serve - Started server process [8]
2025-07-28 13:36:33,133 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 13:36:33,133 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 13:36:33,150 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 13:36:33,151 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,153 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 13:36:33,153 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,154 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 13:36:33,154 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,155 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 13:36:33,155 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 13:36:33,156 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00032s] {}
2025-07-28 13:36:33,156 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 13:36:33,157 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 13:36:33,157 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 13:36:33,157 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 13:36:33,158 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,164 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 13:36:33,164 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,166 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 13:36:33,166 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,169 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 13:36:33,169 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,171 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 13:36:33,171 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 13:36:33,173 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 13:36:33,174 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 13:36:33,174 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 13:36:33,174 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 13:36:33,174 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-28 14:15:28,098 - uvicorn.error - INFO - server - shutdown - Shutting down
2025-07-28 14:15:28,200 - uvicorn.error - INFO - on - shutdown - Waiting for application shutdown.
2025-07-28 14:15:28,200 - main - INFO - main - lifespan - Shutting down Record FastAPI Service...
2025-07-28 14:15:28,200 - uvicorn.error - INFO - on - shutdown - Application shutdown complete.
2025-07-28 14:15:28,200 - uvicorn.error - INFO - server - _serve - Finished server process [8]
2025-07-28 14:18:57,981 - uvicorn.error - INFO - server - _serve - Started server process [8]
2025-07-28 14:18:57,983 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 14:18:57,983 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 14:18:57,998 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 14:18:57,998 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:57,999 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 14:18:58,000 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,000 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 14:18:58,000 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,002 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:18:58,002 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 14:18:58,002 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00050s] {}
2025-07-28 14:18:58,003 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:18:58,003 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 14:18:58,004 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:18:58,004 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 14:18:58,004 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,009 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 14:18:58,010 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,012 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 14:18:58,012 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,014 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 14:18:58,014 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,016 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 14:18:58,016 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 14:18:58,018 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 14:18:58,018 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 14:18:58,019 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 14:18:58,019 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 14:18:58,019 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-28 14:19:56,537 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:19:56,538 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 14:19:56,538 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 58.54s ago] {}
2025-07-28 14:19:56,539 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:19:56,539 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 14:19:56,540 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:19:56,540 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 14:19:56,540 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00026s] {}
2025-07-28 14:19:56,540 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 14:19:56,541 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00019s] {}
2025-07-28 14:19:56,541 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:19:56,541 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 14:19:56,542 - uvicorn.access - INFO - httptools_impl - send - **********:55800 - "GET /health HTTP/1.1" 200
2025-07-28 14:20:12,644 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:20:12,644 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 14:20:12,644 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 74.64s ago] {}
2025-07-28 14:20:12,645 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:20:12,645 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 14:20:12,645 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:20:12,646 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 14:20:12,646 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 16.11s ago] {}
2025-07-28 14:20:12,646 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 14:20:12,646 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 16.11s ago] {}
2025-07-28 14:20:12,647 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:20:12,647 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 14:20:12,647 - uvicorn.access - INFO - httptools_impl - send - **********:47306 - "GET /health HTTP/1.1" 200
2025-07-28 14:22:30,778 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:22:30,779 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 14:22:30,779 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 212.8s ago] {}
2025-07-28 14:22:30,779 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:22:30,780 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 14:22:30,780 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:22:30,781 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 14:22:30,781 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 154.2s ago] {}
2025-07-28 14:22:30,781 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 14:22:30,782 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 154.2s ago] {}
2025-07-28 14:22:30,782 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:22:30,783 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 14:22:30,783 - uvicorn.access - INFO - httptools_impl - send - **********:55426 - "GET /health HTTP/1.1" 200
2025-07-28 14:37:17,022 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:37:17,022 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 14:37:17,022 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 1099s ago] {}
2025-07-28 14:37:17,023 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:37:17,024 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 14:37:17,025 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:37:17,025 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 14:37:17,025 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 1040s ago] {}
2025-07-28 14:37:17,026 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 14:37:17,026 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 1040s ago] {}
2025-07-28 14:37:17,027 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:37:17,027 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 14:37:17,028 - uvicorn.access - INFO - httptools_impl - send - **********:45286 - "GET /health HTTP/1.1" 200
2025-07-28 14:39:21,170 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:39:21,170 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 14:39:21,170 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 1223s ago] {}
2025-07-28 14:39:21,171 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:39:21,172 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 14:39:21,172 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 14:39:21,172 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT VERSION() as version
2025-07-28 14:39:21,172 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 1165s ago] {}
2025-07-28 14:39:21,173 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE() as database
2025-07-28 14:39:21,173 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [cached since 1165s ago] {}
2025-07-28 14:39:21,174 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 14:39:21,174 - database - ERROR - database - get_database_info - Failed to get database info: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'database' at line 1")
[SQL: SELECT DATABASE() as database]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 14:39:21,175 - uvicorn.access - INFO - httptools_impl - send - **********:39112 - "GET /health HTTP/1.1" 200
2025-07-28 21:58:42,912 - uvicorn.access - INFO - httptools_impl - send - **********:60306 - "GET / HTTP/1.1" 200
2025-07-28 21:58:43,893 - uvicorn.access - INFO - httptools_impl - send - **********:60322 - "GET /favicon.ico HTTP/1.1" 404
2025-07-28 22:33:54,811 - uvicorn.error - INFO - server - shutdown - Shutting down
2025-07-28 22:33:54,953 - uvicorn.error - INFO - on - shutdown - Waiting for application shutdown.
2025-07-28 22:33:54,988 - main - INFO - main - lifespan - Shutting down Record FastAPI Service...
2025-07-28 22:33:55,000 - uvicorn.error - INFO - on - shutdown - Application shutdown complete.
2025-07-28 22:33:55,017 - uvicorn.error - INFO - server - _serve - Finished server process [8]
2025-07-28 22:34:31,899 - uvicorn.error - INFO - server - _serve - Started server process [7]
2025-07-28 22:34:31,901 - uvicorn.error - INFO - on - startup - Waiting for application startup.
2025-07-28 22:34:31,901 - main - INFO - main - lifespan - Starting Record FastAPI Service...
2025-07-28 22:34:31,926 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT DATABASE()
2025-07-28 22:34:31,927 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,928 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@sql_mode
2025-07-28 22:34:31,929 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,930 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT @@lower_case_table_names
2025-07-28 22:34:31,930 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,933 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 22:34:31,933 - sqlalchemy.engine.Engine - INFO - base - _execute_context - SELECT 1
2025-07-28 22:34:31,933 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [generated in 0.00031s] {}
2025-07-28 22:34:31,934 - sqlalchemy.engine.Engine - INFO - base - _connection_rollback_impl - ROLLBACK
2025-07-28 22:34:31,934 - database - INFO - database - check_database_connection - Database connection successful
2025-07-28 22:34:31,935 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - BEGIN (implicit)
2025-07-28 22:34:31,935 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`users`
2025-07-28 22:34:31,935 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,942 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`conversations`
2025-07-28 22:34:31,944 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,946 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`messages`
2025-07-28 22:34:31,946 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,948 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`message_costs`
2025-07-28 22:34:31,948 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,950 - sqlalchemy.engine.Engine - INFO - base - _execute_context - DESCRIBE `vdb_records`.`system_info`
2025-07-28 22:34:31,950 - sqlalchemy.engine.Engine - INFO - base - _execute_context - [raw sql] {}
2025-07-28 22:34:31,952 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - COMMIT
2025-07-28 22:34:31,952 - database - INFO - database - init_database - Database tables created successfully
2025-07-28 22:34:31,952 - main - INFO - main - lifespan - Database initialized successfully
2025-07-28 22:34:31,952 - main - INFO - main - lifespan - Record FastAPI Service started successfully
2025-07-28 22:34:31,954 - uvicorn.error - INFO - on - startup - Application startup complete.
2025-07-29 15:10:26,150 - uvicorn.access - INFO - httptools_impl - send - 167.94.138.205:43930 - "GET / HTTP/1.1" 200
2025-07-29 15:10:46,430 - uvicorn.access - INFO - httptools_impl - send - 167.94.138.205:42042 - "GET / HTTP/1.1" 200
2025-07-29 15:10:48,107 - uvicorn.access - INFO - httptools_impl - send - 167.94.138.205:59782 - "GET /favicon.ico HTTP/1.1" 404
2025-07-29 15:10:50,946 - uvicorn.error - WARNING - httptools_impl - data_received - Invalid HTTP request received.
2025-07-29 15:11:06,143 - uvicorn.access - INFO - httptools_impl - send - 167.94.138.205:40860 - "GET / HTTP/1.1" 200
2025-07-29 15:15:10,646 - uvicorn.access - INFO - httptools_impl - send - 220.196.160.154:42084 - "GET / HTTP/1.1" 200
2025-07-29 15:16:01,099 - uvicorn.access - INFO - httptools_impl - send - 220.196.160.53:43530 - "GET / HTTP/1.1" 200
